import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { Typography, Spin, Row, Col, Image, Button, Tabs, Alert } from "antd";

import dayjs from "dayjs";
import "../../../../assets/css/Banner/Banner.css";

import { axiosInstance } from "../../../../apiCalls";
import MappedStores from "../MappedStoresToCreativeFile/MappedStores";
import { CreativeFileObject } from "../../../../types/CreativeFileType/CreativeFileType";
import Link from "../../../UI/Link";
import Btn from "../../../UI/Btn";
import BackButton from "../../../UI/BackButton";

const { Title, Text } = Typography;

const ASSETS_URL = import.meta.env.VITE_ASSETS_URL;

const CreativeFileDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [details, setDetails] = useState<any>(null);
  const [error, setError] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);

  const navigate = useNavigate();
  const location = useLocation();

  const detailsMatch = location.pathname === `/creative-file-details/${id}`;
  const storesMatch =
    location.pathname === `/creative-file-details/${id}/stores`;

  const tabs = [
    { name: "Creative File Details", link: `/creative-file-details/${id}` },
    { name: "Mapped Stores", link: `/creative-file-details/${id}/stores` },
  ];

  // const tabItems = tabs.map((tab) => ({
  //   key: tab.link,
  //   label: <span className="custom-tab">{tab.name}</span>,
  //   children: null,
  // }));

  const tabItems = tabs.map((tab) => ({
    key: tab.link,
    label: (
      <Link
        to={tab.link}
        className={`link-tab ${
          location.pathname === tab.link ? "active-tab" : ""
        }`}
      >
        {tab.name}
      </Link>
    ),
    children: null,
  }));

  const getBannerDetails = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `cms/menu/creatives-details/${id}/`
      );
      // console.log("Details", response.data);
      if (response.status === 200) {
        setDetails(response.data);
      } else {
        setError("Failed to fetch data");
      }
    } catch (error: any) {
      setError(error.response?.data?.message || "Unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getBannerDetails();
  }, [id]);

  const formatText = (text: string) => {
    if (!text) return "-";

    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const renderDetailsInCard = () => {
    if (!details) return null;

    const fields: { label: string; key: keyof CreativeFileObject }[] = [
      { label: "Name", key: "name" },
      { label: "Type", key: "type" },
      { label: "Status", key: "is_active" },

      { label: "Created Date ", key: "created_at" },
      { label: "Updated Date ", key: "updated_at" },
      { label: "Content Type", key: "content_type" },
    ];

    return fields.map((field) => {
      const value = details[field.key];
      let displayValue: React.ReactNode;
      if (
        field.key === "type" ||
        (field.key === "content_type" && typeof value === "string")
      ) {
        displayValue = formatText(value);
      } else if (field.key === "created_at" && typeof value === "string") {
        displayValue = formatDate(value);
      } else if (field.key === "updated_at" && typeof value === "string") {
        displayValue = formatDate(value);
      } else if (typeof value === "boolean") {
        displayValue = value ? "Active" : "Inactive";
      } else if (value === null || value === undefined) {
        displayValue = "-";
      } else if (typeof value === "object") {
        // Display a placeholder message for objects
        displayValue = "Details available";
      } else {
        displayValue = value; // Handle strings and numbers directly
      }

      return (
        <div key={field.key.toString()} className="order-details-value">
          <div className="order-details-label">{field.label}</div>
          <span className="order-details-value-colon">:</span>
          <span className="order-details-value-value">{displayValue}</span>
        </div>
      );
    });
  };

  const formatDate = (dateString: string): string => {
    return dayjs(dateString).format("DD/MM/YYYY, hh:mm A");
  };

  // const MappedStores = () => {
  //   const column = [
  //     {
  //       title: "Store Name",
  //       dataIndex: "store_name",
  //       key: "store_name",
  //       width: "20%",
  //       render: (text: string, record: StoreObject) =>
  //         text ? (
  //           <Link
  //             className="common-link text-decoration-none"
  //             to={`/stores/${record.store_id}/details`}
  //           >
  //             {text}
  //           </Link>
  //         ) : (
  //           "N/A"
  //         ),
  //     },
  //     {
  //       title: "Store Code",
  //       dataIndex: "store_code",
  //       key: "store_code",
  //       width: "20%",
  //     },
  //     {
  //       title: "Active Status",
  //       dataIndex: "is_active",
  //       key: "is_active",
  //       width: "10%",
  //       render: (isActive: boolean) => (isActive ? "Yes" : "No"),
  //     },
  //   ];
  //   return (
  //     <div className="section">
  //       <Col span={24} style={{ padding: "20px" }}>
  //         {details?.stores?.length ? (
  //           <>
  //             {/* <Title level={4} style={{ marginBottom: "16px", color: "#333" }}>
  //               Stores:
  //             </Title>
  //             <Row gutter={[16, 16]} justify="start">
  //               {details.stores.map((store: StoreObject) => (
  //                 <Col
  //                   key={store.store_id}
  //                   xs={24}
  //                   sm={12}
  //                   md={8}
  //                   lg={6}
  //                   xl={4}
  //                 >
  //                   <Link
  //                     to={`/stores/${store.store_id}/details`}
  //                     style={{ textDecoration: "none" }}
  //                   >
  //                     <Card
  //                       hoverable
  //                       style={{
  //                         height: "10vh",
  //                         display: "flex",
  //                         justifyContent: "center",
  //                         alignItems: "center",
  //                         textAlign: "center",
  //                         borderRadius: "8px",
  //                         boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
  //                         color: "#FF8732",
  //                       }}
  //                     >
  //                       {store?.store_name} ({store?.store_code})
  //                     </Card>
  //                   </Link>
  //                 </Col>
  //               ))}
  //             </Row> */}
  //             <div className="mt-2">
  //               {" "}
  //               <Table
  //                 dataSource={details.stores}
  //                 columns={column}
  //                 pagination={false}
  //               />
  //             </div>
  //           </>
  //         ) : (
  //           <Empty
  //             description="No Store Mapped Yet"
  //             image={Empty.PRESENTED_IMAGE_SIMPLE}
  //             style={{ marginTop: "20px" }}
  //           />
  //         )}
  //       </Col>
  //     </div>
  //   );
  // };

  const renderBannerPages = () => {
    if (!details?.source || !details.source.length) return null;

    return (
      <div className="section">
        <Title level={4}>Preview</Title>
        <Row gutter={[16, 16]} justify="start">
          <Col xs={24} sm={12} md={8} lg={6}>
            {details.source ? (
              details.content_type.includes("image") ? (
                <Image
                  src={
                    details.source.startsWith("http")
                      ? details.source
                      : `${ASSETS_URL}/${details.source}`
                  }
                  alt="Banner Page"
                  className="image-preview"
                  // width={200}
                  // style={{ display: "block", margin: "auto" }}
                  preview={true}
                />
              ) : details.content_type.includes("video") ? (
                <video
                  controls
                  src={
                    details.source.startsWith("http")
                      ? details.source
                      : `${ASSETS_URL}/${details.source}`
                  }
                  className="video-preview"
                  // style={{
                  //   border: "1px solid #ddd",
                  //   width: "200px",
                  //   height: "200px",
                  //   display: "block",
                  //   margin: "auto",
                  //   marginBottom: "10px",
                  // }}
                >
                  Your browser does not support the video tag.
                </video>
              ) : details.content_type.includes("text") ? (
                <Typography className="family-Poppins">
                  {details.source}
                </Typography>
              ) : null
            ) : (
              <Text type="warning">No source available</Text>
            )}
          </Col>
        </Row>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <Spin size="large" />
      </div>
    );
  }
  // if (error) return <Text type="danger">{error}</Text>;
  if (!loading && error) {
    return (
      <div className="error-container">
        <Alert message="Error" description={error} type="error" showIcon />
        <div className="retry-button d-flex justify-content-center align-items-center mt-3">
          <Button type="primary" onClick={() => navigate(-1)}>
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  if (!details) return <Text>No details found</Text>;

  return (
    <>
      <BackButton />
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Creative File Details</div>
        <div className="d-flex">
          <Link to={`/map-stores-to-creative-file/${id}`}>
            <Btn
              type="primary"
              // onClick={() => navigate(`/map-stores-to-creative-file/${id}`)}
            >
              Map to Stores
            </Btn>
          </Link>
        </div>
      </div>
      <div className="w-full max-w-3xl mx-auto m-2">
        {/* Tab Navigation */}
        <div className="tabs-navigation mt-4">
          <Tabs
            activeKey={location.pathname}
            tabBarStyle={{ borderBottom: "none" }}
            onChange={(key) => navigate(key)}
            items={tabItems}
          />
        </div>

        <div className="max-w-3xl w-full">
          {detailsMatch && (
            <div>
              <div className="banner-details-container d-flex gap-3 p-2 flex-wrap justify-content-between align-items-center">
                {/* Left Side */}
                <div className="left-section" style={{ flex: 1 }}>
                  {renderDetailsInCard()}
                </div>

                {/* <Divider className="divider" type="vertical" /> */}

                {/* Right Side */}
                <div className="right-section" style={{ flex: 1 }}>
                  {renderBannerPages()}
                </div>
              </div>
              {/* <div className="banner-details-container" style={{ padding: "0px" }}>
        {MappedStores()}
      </div> */}
            </div>
          )}
          {storesMatch && <MappedStores />}
        </div>
      </div>
    </>
  );
};

export default CreativeFileDetails;

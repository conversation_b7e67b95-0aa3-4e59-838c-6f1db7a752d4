import React from "react";
import { MenuOutlined } from "@ant-design/icons";
import { Divider } from "antd";

interface SidebarHeaderProps {
  logo: string;
  collapsed: boolean;
  setCollapsed: (value: boolean) => void;
}

const SidebarHeader: React.FC<SidebarHeaderProps> = ({
  logo,
  collapsed,
  setCollapsed,
}) => {
  return (
    <>
      <div
        className={`d-flex flex-wrap align-items-center ${
          collapsed ? "justify-content-center" : "justify-content-between"
        }  ps-3 py-2`}
      >
        {!collapsed && <img src={logo} alt="Logo" className="logo" />}
        {/* <h6 >{collapsed ? "" : "BK-CMS"}</h6> */}
        <MenuOutlined
          className="menu-icon border-1-solid border-secondary"
          onClick={() => setCollapsed(!collapsed)}
        />
      </div>

      <Divider className="m-0 mb-2" />
    </>
  );
};

export default SidebarHeader;

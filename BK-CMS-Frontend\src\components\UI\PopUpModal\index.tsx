// src/utils/showConfirmActionModal.ts
import { Modal } from "antd";

interface ShowConfirmActionModalProps {
  isActive: boolean;
  onConfirm: () => Promise<void> | void;
  entityName?: string;
}

const showConfirmActionModal = ({
  isActive,
  onConfirm,
  entityName = "this item",
}: ShowConfirmActionModalProps): void => {
  const actionText = isActive ? "activate" : "deactivate";

  Modal.confirm({
    title: isActive
      ? `Activate ${entityName}`
      : `Deactivate ${entityName}`,
    content: `Are you sure you want to ${actionText} ${entityName}?`,
    okText: "Yes",
    cancelText: "No",
    className: "custom-modal",
    okButtonProps: { className: "custom-modal-ok-button" },
    cancelButtonProps: { className: "custom-modal-cancel-button" },
    onOk: onConfirm,
  });
};

export default showConfirmActionModal;

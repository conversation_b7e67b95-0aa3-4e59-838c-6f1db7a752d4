import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON>, Spin, Button, message, Tabs } from "antd";

import "../../../assets/css/Kiosk/KiosksDetails.css";
import { KioskType } from "../../../types/KioskType/KioskType";
import { axiosInstance } from "../../../apiCalls";
import dayjs from "dayjs";
import { EditOutlined } from "@ant-design/icons";
import { Edit } from "../../../constants/Constant";
import MappedStoreKioskFeatures from "./KioskFeatures/StoreKioskFeatures";
import BackButton from "../../UI/BackButton";
import Link from "../../UI/Link";

const StoreKioskDetails: React.FC = () => {
  const { storeId, kioskId } = useParams<{
    storeId: string;
    kioskId: string;
  }>();
  const [kiosksDetails, setKiosksDetails] = useState<KioskType | undefined>(
    undefined
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const navigate = useNavigate();

  const detailsMatch =
    location.pathname === `/store-kiosks-details/${storeId}/${kioskId}`;
  const storesMatch =
    location.pathname === `/store/features/${storeId}/${kioskId}/kiosks`;

  const tabs = [
    {
      name: "Store Kiosk Details",
      link: `/store-kiosks-details/${storeId}/${kioskId}`,
    },
    {
      name: "Kiosk Features",
      link: `/store/features/${storeId}/${kioskId}/kiosks`,
    },
  ];

  const tabItems = tabs.map((tab) => ({
    key: tab.link,
    label: (
      <Link
        to={tab.link}
        className={`link-tab ${
          location.pathname === tab.link ? "active-tab" : ""
        }`}
      >
        {tab.name}
      </Link>
    ),
    children: null,
  }));

  // Fetch the kiosk details based on the ID
  const fetchKioskDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.get(`cms/stores/${storeId}/kiosks/`);
      if (response.status === 200) {
        const kioskData = response.data.objects.find(
          (kiosk: any) => kiosk.id === parseInt(kioskId!)
        );

        if (kioskData) {
          setKiosksDetails(kioskData);
        } else {
          setError("Kiosk not found.");
        }
      } else {
        setError("Failed to load kiosk details.");
      }
    } catch (err) {
      console.error("Error fetching kiosk details:", err);
      setError("Failed to load kiosk details.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchKioskDetails();
  }, [storeId, kioskId]);

  const fields = [
    { label: "Store", key: "store" as keyof KioskType },
    { label: "Kiosk Name", key: "name" as keyof KioskType },
    { label: "Code", key: "code" as keyof KioskType },
    { label: "Kiosk Type", key: "type" as keyof KioskType },
    {
      label: "Store Location Type",
      key: "store_location_type" as keyof KioskType,
    },
    { label: "EDC Store ID", key: "edc_store_id" as keyof KioskType },
    {
      label: "EDC Security Token",
      key: "edc_securitytoken" as keyof KioskType,
    },
    { label: "EDC Client ID", key: "edc_clientiD" as keyof KioskType },
    { label: "Teamviewer ID", key: "teamviewer_id" as keyof KioskType },
    { label: "Anydesk ID", key: "anydesk_id" as keyof KioskType },

    //{ label: "EDC Serial Number", key: "edc_serial_number" as keyof KioskType },
    { label: "Created At", key: "created_at" as keyof KioskType },
    { label: "Updated At", key: "updated_at" as keyof KioskType },
    {
      label: "Printer Serial Number",
      key: "printer_serial_number" as keyof KioskType,
    },
    { label: "Token", key: "token" as keyof KioskType },
    // { label: "FCM Token", key: "fcm_token" as keyof KioskType },
    { label: "Active Status", key: "is_active" as keyof KioskType },
    { label: "Version", key: "version" as keyof KioskType },
  ];

  const formatDate = (date: string | number): string => {
    return dayjs(date).format("DD/MM/YYYY, hh:mm A");
  };

  const renderDetailsInCard = () => {
    if (!kiosksDetails) return null;

    return fields.map((field) => {
      const value = kiosksDetails[field.key];
      let displayValue: React.ReactNode;

      if (
        field.key === "created_at" ||
        (field.key === "updated_at" && typeof value === "string")
      ) {
        displayValue = formatDate(value.toString());
      } else if (typeof value === "boolean") {
        displayValue = value ? "Yes" : "No";
      } else if (value === null || value === undefined || value === "") {
        displayValue = "-";
      } else if (field.key === "version") {
        displayValue = parseFloat(value.toString()).toFixed(1);
      } else {
        displayValue = value;
      }
      return (
        <div key={field.key} className="order-details-value">
          <div className="order-details-label">{field.label}</div>
          <span className="order-details-value-colon">:</span>
          <span className="order-details-value-value">{displayValue}</span>
        </div>
      );
    });
  };

  if (loading) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ height: "100vh" }}
      >
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return <Alert message={error} type="error" />;
  }

  if (!kiosksDetails) {
    return <Alert message="Kiosk not found." type="warning" />;
  }

  return (
    <div className="">
      <div className="d-flex justify-content-start align-items-center">
        <BackButton to={`/stores/${storeId}/kiosk`} />
      </div>
      <div className="w-full max-w-3xl mx-auto">
        {/* Tab Navigation */}
        <div className="tabs-navigation">
          <Tabs
            activeKey={location.pathname}
            tabBarStyle={{ borderBottom: "none" }}
            onChange={(key) => navigate(key)}
            items={tabItems}
          />
        </div>
      </div>
      {detailsMatch && (
        <>
          <div className="d-flex justify-content-between align-items-start w-100">
            <div className="order-details-card">{renderDetailsInCard()}</div>

            <div className="title-and-add-btn me-3">
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => {
                  if (storeId && kioskId) {
                    navigate(`/stores/${storeId}/kiosks/${kioskId}/update/`);
                  } else {
                    message.error("Invalid store or kiosk ID");
                  }
                }}
              >
                {Edit}
              </Button>
            </div>
          </div>
        </>
      )}
      {storesMatch && kioskId && <MappedStoreKioskFeatures kioskId={kioskId} />}
    </div>
  );
};

export default StoreKioskDetails;

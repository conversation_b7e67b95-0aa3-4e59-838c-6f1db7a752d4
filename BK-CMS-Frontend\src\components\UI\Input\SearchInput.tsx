import {
  forwardRef,
  useCallback,
  useState,
  useImperativeHandle,
  memo,
  useRef,
} from "react";
import { Input, InputRef } from "antd";
import { SearchOutlined } from "@ant-design/icons";

interface SearchInputProps {
  value: string;
  onChange: (val: string) => void;
  onSearch: (val: string) => void;
  placeholder?: string;
}

const SearchInput = forwardRef<InputRef, SearchInputProps>(
  ({ value, onChange, onSearch, placeholder = "Search..." }, ref) => {
    const [isFocused, setIsFocused] = useState(false);
    const inputRef = useRef<InputRef>(null);

    const handleFocus = useCallback(() => setIsFocused(true), []);
    const handleBlur = useCallback(() => setIsFocused(false), []);

    // Forward the ref to the input element
    useImperativeHandle(ref, () => inputRef.current!, []);

    return (
      <div className="search-btn-driver">
        <Input.Search
          ref={inputRef}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onSearch={(val) => {
            if (val.trim()) onSearch(val);
          }}
          onFocus={handleFocus}
          onBlur={handleBlur}
          prefix={
            <SearchOutlined
             className={isFocused ? "" : "invisible"}
            />
          }
          allowClear
        />
      </div>
    );
  }
);

SearchInput.displayName = "SearchInput";

export default memo(SearchInput);

/* Constant styles */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --font-size-button: 16px;
  --font-size-divider: 20px;
  --divider-color: #7cb305;
}

/* Container styles */
.store-container {
  padding: 20px;
}

/* Header styles */
.stores-list-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

/* Button styles */
.add-store-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  font-size: var(--font-size-button);
  font-weight: bold;
  border-radius: 5px;
  cursor: pointer;
}

.add-store-button:hover {
  background-color: var(--secondary-color);
  color: white;
}

/* Divider styles */
.store-divider {
  border-color: var(--divider-color);
  font-weight: bold;
  font-size: var(--font-size-divider);
}

/* Table styles */
.store-table {
  margin-top: 20px;
}

/* Pagination styles */
.store-pagination {
  text-align: center;
  margin-top: 20px;
}

/* Store name styles */
.store-name {
  cursor: pointer;
  color: var(--primary-color);
  text-decoration: underline;
}

.store-name:hover {
  color: var(--secondary-color);
}

/* Loading and error messages */
.loading,
.error {
  text-align: center;
  font-weight: bold;
  color: var(--secondary-color);
}

.tabs-navigation {
  display: flex;
  flex-wrap: wrap; /* Allows tabs to go to the next line */
  gap: 12px; /* Adds space between tabs */
  align-items: center;
  padding: 10px 16px;
}

.ant-tabs-nav {
  flex-wrap: wrap !important; /* Forces tabs to wrap */
  display: flex !important;
  justify-content: center; /* Center tabs properly */
}

.ant-tabs-nav-list {
  flex-wrap: wrap !important;
  display: flex !important;
}
.link-tab {
  color: inherit;
  text-decoration: none;
  /* padding: 4px 6px; */
}

/* .link-tab {
  color: #ff6600; 
  font-weight: 600;
} */

.link-tab.active-tab {
  color: #ff6600 !important;
  font-weight: 500;
}

.link-tab:hover {
  color: #ff8533; /* Slightly lighter on hover */
}

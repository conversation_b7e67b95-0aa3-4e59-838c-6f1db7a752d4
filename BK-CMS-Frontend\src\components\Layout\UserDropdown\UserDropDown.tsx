import React, { useEffect, useState } from "react";
import { Dropdown, Avatar, MenuProps, notification, message } from "antd";
import { DownOutlined, LogoutOutlined, UserOutlined } from "@ant-design/icons";
import Cookies from "js-cookie";
import { Spinner } from "react-bootstrap";
import {
  ACCESS_TOKEN,
  REFRESH_TOKEN,
  FIRST_NAME,
  LAST_NAME,
  LOGIN_PATH,
  USERNAME,
  ADMIN,
} from "../../../constants/Constant";
import { axiosInstance } from "../../../apiCalls";

interface Props {}

const UserDropdown: React.FC<Props> = () => {
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [username, setUsername] = useState<string | null>(null);

  useEffect(() => {
    const first = Cookies.get(FIRST_NAME);
    const last = Cookies.get(LAST_NAME);
    if (first && last) setUsername(`${first} ${last}`);
  }, []);

  const handleLogout = async () => {
    try {
      const refreshToken = Cookies.get(REFRESH_TOKEN);
      if (!refreshToken) {
        clearSessionAndRedirect();
        return;
      }

      setLoading(true);
      const response = await axiosInstance.post(
        "/cms/accounts/logout/",
        { refresh: refreshToken },
        { headers: { "Content-Type": "application/json" } }
      );
      if (response.status === 200) {
        clearSessionAndRedirect();
        notification.success({
          message: "Logout successful",
          description: response.data?.message || "Logged out successfully",
        });
      }
    } catch {
      message.error("Failed to logout. Please try again.");
      clearSessionAndRedirect();
    } finally {
      setLoading(false);
    }
  };

  const clearSessionAndRedirect = () => {
    [ACCESS_TOKEN, REFRESH_TOKEN, USERNAME, FIRST_NAME, LAST_NAME].forEach(
      (cookieName) => {
        Cookies.remove(cookieName);
      }
    );

    window.location.href = LOGIN_PATH;
  };

  const menuItems: MenuProps["items"] = [
    {
      key: "logout",
      label: (
        <span className="logout-text">
          {loading ? <Spinner className="me-2" /> : null}
          Logout
        </span>
      ),
      icon: <LogoutOutlined className="logout-icon" />,
      onClick: handleLogout,
    },
  ];

  const formatUsername = (text: string | null) => {
    const fallback = Cookies.get(USERNAME) || ADMIN;
    if (!text || text.trim() === "") return fallback;
    return text
      .split(" ")
      .map((word) => word[0].toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  return (
    <Dropdown
      className="logout-dropdown"
      menu={{ items: menuItems }}
      trigger={["click"]}
      open={visible}
      onOpenChange={setVisible}
    >
      <div className={`dropdown-trigger ${visible ? "active" : ""}`}>
        <Avatar className="user-avatar">
          <UserOutlined />
        </Avatar>
        <span className="username">{formatUsername(username)}</span>
        <DownOutlined className={`dropdown-icon ${visible ? "rotate" : ""}`} />
      </div>
    </Dropdown>
  );
};

export default UserDropdown;

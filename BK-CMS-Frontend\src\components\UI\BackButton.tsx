import React from "react";
import { DoubleLeftOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import Btn from "./Btn";
import { BACK } from "../../constants/Constant";

// Define the type for the component props
interface BackButtonProps {
  to?: string;
  onClick?: () => void;
  children?: React.ReactNode;
}

const BackButton: React.FC<BackButtonProps> = ({ onClick, to, children }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (to) {
      navigate(to);
    } else {
      navigate(-1);
    }
  };

  return (
    <Btn
      type="primary"
      shape="round"
      icon={<DoubleLeftOutlined />}
      onClick={handleClick}
      className="backButton"
    >
      {children || BACK}
    </Btn>
  );
};

export default React.memo(BackButton);

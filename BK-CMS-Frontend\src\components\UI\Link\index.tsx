import { Link as RouterLink, To } from "react-router-dom";
import { memo, ReactNode } from "react";

interface LinkProps {
  to: To;
  children: ReactNode;
  className?: string;
  ariaLabel?: string;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
}

const LINK = ({ to, children, className = "", ariaLabel, onClick }: LinkProps) => {
  return (
    <RouterLink
      to={to}
      className={`${className} family-font-Poppin`}
      aria-label={ariaLabel}
      onClick={onClick}
    >
      {children}
    </RouterLink>
  );
};

export default memo(LINK);

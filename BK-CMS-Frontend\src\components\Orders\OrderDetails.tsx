import React, { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import dayjs from "dayjs";
import { <PERSON><PERSON>, <PERSON> } from "antd";
import { axiosInstance } from "../../apiCalls";
import { Order_Detail } from "../../constants/Constant";
import {
  Item,
  OrderType,
  ProcessedProduct,
  ProductDetails,
} from "../../types/OrderType/OrderType";
import "../../assets/css/Orders/OrdersDetails.css";
// import { jsPDF } from "jspdf";
import "jspdf-autotable";
import { DoubleRightOutlined } from "@ant-design/icons";

const OrderDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [order, setOrder] = useState<OrderType | undefined>(undefined);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [productDetails, setProductDetails] = useState<any[]>([]);

  // console.log("OrdersDetails", order);
  // console.log("OrdersDetails Product Details", productDetails);

  const fetchOrderDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.get(`pos/orders/orders/${id}/`);
      if (response.status === 200) {
        const orderData = response.data;
        setOrder(orderData);
        if (orderData.product_details?.items) {
          processProductDetails(orderData.product_details);
        }
      } else {
        setError("Failed to load order details.");
      }
    } catch (error) {
      console.error("Error fetching order details:", error);
      setError("Failed to load order details.");
    } finally {
      setLoading(false);
    }
  };

  const processProductDetails = (productDetails: ProductDetails) => {
    const CGST = productDetails.cgst;
    const SGST = productDetails.sgst;
    const items = productDetails.items;
    const formattedItems: ProcessedProduct[] = Object.values(items).map(
      (item: Item) => {
        const chooseSide = item.sections?.choose_side?.name || "";
        const chooseDrink = item.sections?.choose_drink?.name || "";

        // const modifiers = Object.entries(item.sections || {}).flatMap(
        //   ([_, section]: any) =>
        //     Object.entries(section.modifier_groups || {}).flatMap(
        //       ([__, group]: any) =>
        //         Object.entries(group).map(([___, value]: any) => ({
        //           code: value.code,
        //           modifier: value.name,
        //           quantity: value.quantity,
        //         }))
        //     )
        // );
        const modifiers = Object.entries(item.sections || {}).flatMap(
          ([_, section]: any) =>
            Object.entries(section.modifier_groups || {}).flatMap(
              ([__, group]: any) =>
                Object.entries(group)
                  .filter(([_, val]: [string, any]) => val?.code)
                  .map(([_, val]: [string, any]) => ({
                    code: val.code,
                    modifier: val.name,
                    quantity: val.quantity,
                  }))
            )
        );

        return {
          name: item.name,
          chooseSide: chooseSide,
          chooseDrink: chooseDrink,
          modifiers: modifiers,
          quantity: item.quantity,
          subTotal: item.sub_total,
          cgst: CGST,
          sgst: SGST,
        };
      }
    );

    setProductDetails(formattedItems);
  };

  const formatValue = (text: string) => {
    if (!text) return "-";
    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const renderDetailsInCard = () => {
    if (!order) return null;

    const fields: { label: string; key: keyof OrderType }[] = [
      // { label: "Order ID", key: "id" },
      { label: "Store Name", key: "store" },
      { label: "Status", key: "order_status" },
      { label: "Customer Name", key: "customer_name" },
      { label: "Customer Phone", key: "customer_phone" },
      { label: "Payment ID", key: "payment_id" },
      { label: "Kiosk ID", key: "kiosk_terminal_id" },
      { label: "OrderType", key: "order_type" },
      { label: "Pickup Type", key: "pickup_type" },
      { label: "Table Number", key: "table_number" },
      { label: "Payment Method", key: "payment_method" },
      { label: "Payment Status", key: "payment_status" },
      { label: "Created", key: "created_at" },
      { label: "Donation Enabled", key: "donation_enabled" },
      { label: "Synced", key: "synced" },
    ];

    return fields.map((field) => {
      const value = order[field.key];
      let displayValue: React.ReactNode;

      if (field.key === "created_at" && typeof value === "string") {
        displayValue = formatDate(value);
      } else if (typeof value === "boolean") {
        displayValue = value ? "Yes" : "No";
      } else if (value === null || value === undefined || value === "") {
        displayValue = "-";
      } else if (
        (field.key === "order_type" && typeof value === "string") ||
        (field.key === "payment_method" && typeof value === "string") ||
        (field.key === "payment_status" && typeof value === "string") ||
        (field.key === "order_status" && typeof value === "string")
      ) {
        displayValue = formatValue(value);
      } else if (field.key === "store" && typeof value === "string") {
        displayValue = (
          <Link
            className="common-link text-decoration-none"
            to={`/stores/${order.store_id}/details`}
          >
            {value}
          </Link>
        );
      } else if (typeof value === "object") {
        // Display a placeholder message for objects
        displayValue = "Details available";
      } else {
        displayValue = value; // Handle strings and numbers directly
      }

      return (
        <div key={field.key.toString()} className="order-details-value">
          <div className="order-details-label">{field.label}</div>
          <span className="order-details-value-colon">:</span>
          <span className="order-details-value-value">{displayValue}</span>
        </div>
      );
    });
  };

  const formatDate = (dateString: string): string => {
    return dayjs(dateString).format("DD/MM/YYYY, hh:mm A");
  };

  // const generatePDF = async () => {
  //   if (!order) {
  //     console.error("No order data available.");
  //     return;
  //   }

  //   const doc = new jsPDF({
  //     orientation: "p",
  //     unit: "mm",
  //     format: "a4",
  //   });

  //   // **Invoice Header**
  //   doc.setFontSize(18);
  //   doc.setFont("helvetica", "bold");
  //   doc.text("INVOICE", 105, 15, { align: "center" });

  //   doc.setFontSize(12);
  //   doc.setFont("helvetica", "normal");
  //   doc.text(`# ${order?.id}`, 105, 25, { align: "center" });

  //   // **Order Details**
  //   doc.setFontSize(10);
  //   doc.text("Tanggal Pembelian:", 15, 40);
  //   doc.text(formatDate(order?.created_at || ""), 55, 40);

  //   doc.text("Pengiriman:", 15, 50);
  //   doc.setFont("helvetica", "bold");
  //   doc.text(order?.store || "BK India Staging", 55, 50);
  //   doc.setFont("helvetica", "normal");

  //   doc.text(
  //     "Jl. Meruya Selatan No.5c, RT.4/RW.4,\nMeruya Utara, Kec. Kembangan, Kota\nJakarta Barat, Daerah Khusus Ibukota\nJakarta 11610",
  //     55,
  //     56
  //   );

  //   doc.text("Penerima:", 15, 80);
  //   doc.text("-", 55, 80);

  //   doc.text("Courier:", 15, 90);
  //   doc.text("-", 55, 90);

  //   doc.text("Payment Method:", 15, 100);
  //   doc.text(order?.payment_method || "-", 55, 100);

  //   doc.text("Payment Status:", 15, 110);
  //   doc.text(order?.payment_status || "-", 55, 110);

  //   // **Separator Line**
  //   doc.setLineWidth(0.5);
  //   doc.line(15, 118, 195, 118);

  //   // **Table Headers & Data**
  //   const headers = [["Name", "Modifiers", "Quantity", "Sub Total"]];
  //   const data = productDetails.map((item) => [
  //     item.name,
  //     item.modifiers.map((mod: any) => mod.modifier).join(", "),
  //     item.quantity,

  //     `${(item.quantity * item.subTotal).toFixed(2)}`,
  //   ]);

  //   (doc as any).autoTable({
  //     startY: 123,
  //     head: headers,
  //     body: data,
  //     theme: "striped",
  //     styles: { fontSize: 10, cellPadding: 4 },
  //     headStyles: { fillColor: [248, 249, 250], textColor: [0, 0, 0] },
  //     margin: { left: 15, right: 15 },
  //   });

  //   // **Pricing Summary**
  //   const finalY = (doc as any).lastAutoTable.finalY || 130;
  //   const totalAmount = order.product_details?.total?.toFixed(2) || "-";
  //   const discount = order?.discount_value || "-";
  //   const taxableValue = order?.taxable_value ?? "-";
  //   const grandTotal = order?.grand_total ?? "-";

  //   const summaryY = finalY + 10;
  //   doc.setFontSize(10);

  //   doc.text("Sub Total:", 140, summaryY);
  //   doc.text(`${totalAmount}`, 180, summaryY, { align: "right" });

  //   doc.text("Discount:", 140, summaryY + 10);
  //   doc.text(`${discount}`, 180, summaryY, { align: "right" });

  //   doc.text("Taxable Value:", 140, summaryY + 20);
  //   doc.text(`${taxableValue}`, 180, summaryY + 20, {
  //     align: "right",
  //   });

  //   // **Separator Line Before Grand Total**
  //   doc.setLineWidth(0.5);
  //   doc.line(150, summaryY + 30, 195, summaryY + 30);

  //   // ** Grand Total **
  //   doc.setFontSize(12);
  //   doc.setFont("helvetica", "bold");
  //   doc.text("Grand Total:", 140, summaryY + 40);
  //   doc.text(`${grandTotal}`, 180, summaryY + 40, {
  //     align: "right",
  //   });

  //   // **Footer**
  //   doc.setFontSize(10);
  //   doc.setFont("helvetica", "normal");
  //   doc.text("Contact Us: +6280000112244 | <EMAIL>", 15, 280);

  //   // **Convert to Blob URL & Print**
  //   const pdfBlob = doc.output("blob");
  //   const pdfUrl = URL.createObjectURL(pdfBlob);
  //   const printWindow = window.open(pdfUrl);
  //   if (printWindow) {
  //     printWindow.onload = () => {
  //       printWindow.focus();
  //       printWindow.print();
  //     };
  //   } else {
  //     console.error("Unable to open print window");
  //   }
  // };

  useEffect(() => {
    fetchOrderDetails();
  }, [id]);

  if (loading) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ height: "100vh" }}
      >
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return <Alert message={error} type="error" />;
  }

  if (!order) {
    return <Alert message="Order not found." type="warning" />;
  }

  return (
    <div className="max-w-3xl w-full">
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>
          {" "}
          {Order_Detail} <DoubleRightOutlined /> {order.id}
        </div>
        {/* <div className="d-flex">
          <div>
            <Button
              type="primary"
              icon={<PrinterOutlined />}
              onClick={generatePDF}
              className="mb-2"
            >
              Print Invoice
            </Button>
          </div>
        </div> */}
      </div>

      <div className="order-details-card mt-3">{renderDetailsInCard()}</div>

      <div className="items-container">
        <h3 className="items-title">Items</h3>
        <table className="items-table">
          <thead>
            <tr className="items-table-header">
              <th>Name</th>
              <th>Modifiers</th>
              <th>Quantity</th>
              <th>Sub Total</th>
            </tr>
          </thead>
          <tbody>
            {productDetails.map((item: any, index: number) => (
              <tr key={index} className="items-table-row">
                <td>{item.name}</td>
                <td>
                  {item.modifiers.map((mod: any, modIndex: number) => (
                    <div key={modIndex}>
                      {mod.quantity} x {mod.code} ({mod.modifier})
                      {/* ({item.chooseSide}) ({item.chooseDrink}) */}
                    </div>
                  ))}
                </td>
                <td>{item.quantity}</td>
                <td>{item.subTotal.toFixed(2) || "-"}</td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Sub Total and Grand Total Section */}
        <div className="totals-container">
          <table className="totals-table">
            <tbody>
              <tr>
                <td className="totals-label">Sub Total:</td>
                <td className="totals-value">
                  {order.product_details?.sub_total?.toFixed(2) || "-"}
                </td>
              </tr>
              {/* <tr>
                <td className="totals-label">Discount Value:</td>
                <td className="totals-value">
                  {order.discount_value?.toFixed(2) || "0.00"}
                </td>
              </tr> */}
              {/* <tr>
                <td className="totals-label">Taxable Value:</td>
                <td className="totals-value">
                  {order.taxable_value?.toFixed(2) || "-"}
                </td>
              </tr> */}
              <tr>
                <td className="totals-label">SGST:</td>
                <td className="totals-value">
                  {productDetails[0].sgst?.toFixed(2) || "-"}
                </td>
              </tr>
              <tr>
                <td className="totals-label">CGST:</td>
                <td className="totals-value">
                  {productDetails[0].cgst?.toFixed(2) || "-"}
                </td>
              </tr>
              <tr>
                <td className="totals-grand-label">Grand Total:</td>
                <td className="totals-grand-value">
                  {order.grand_total?.toFixed(2) || "-"}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;
